/* ===== ADMIN PANEL STYLES ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    color: #1a202c;
    line-height: 1.6;
}

/* ===== CONTAINER & LAYOUT ===== */

.admin-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== NAVIGATION BAR ===== */

.admin-nav {
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.25rem;
    color: #2d3748;
}

.brand-icon {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.nav-btn {
    background: transparent;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    transition: all 0.2s;
    white-space: nowrap;
}

.nav-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.nav-btn.active {
    background: #3182ce;
    color: white;
    border-color: #3182ce;
}

.nav-actions {
    display: flex;
    gap: 0.5rem;
}

.logout-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background 0.2s;
}

.logout-btn:hover {
    background: #c53030;
}

/* ===== MAIN CONTENT ===== */

.admin-main {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* ===== SECTIONS ===== */

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.section-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #2d3748;
}

/* ===== BUTTONS ===== */

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #3182ce;
    color: white;
}

.btn-primary:hover {
    background: #2c5aa0;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
}

/* ===== FORMS ===== */

.form-container {
    background: white;
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.admin-form {
    width: 100%;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group select[multiple] {
    min-height: 120px;
}

.form-group small {
    color: #718096;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
}

/* ===== TIMETABLE GRID ===== */

.timetable-grid {
    margin: 2rem 0;
}

.timetable-grid h3 {
    margin-bottom: 1rem;
    color: #2d3748;
    font-size: 1.25rem;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.day-schedule h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.day-schedule textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    min-height: 80px;
    resize: vertical;
}

/* ===== FACULTY MEMBERS ===== */

.faculty-list-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: 0.5rem;
}

.faculty-list-section h3 {
    margin-bottom: 1rem;
    color: #2d3748;
}

.faculty-member {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
}

.remove-faculty {
    align-self: end;
    margin-top: 1.5rem;
}

/* ===== DATA LISTS ===== */

.data-list {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.data-item {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.data-item:last-child {
    border-bottom: none;
}

.data-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.data-info p {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.data-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.data-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

/* ===== LOADING & MESSAGES ===== */

.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    color: #4a5568;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3182ce;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message-container {
    margin-bottom: 1rem;
}

.message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message.success {
    background: #f0fff4;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.message.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #feb2b2;
}

/* ===== MODALS ===== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-body {
    padding: 1.5rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .admin-nav {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .nav-menu {
        justify-content: center;
        width: 100%;
    }
    
    .nav-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .admin-main {
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .days-grid {
        grid-template-columns: 1fr;
    }
    
    .data-item {
        flex-direction: column;
        gap: 1rem;
    }
    
    .data-actions {
        align-self: stretch;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .nav-menu {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        width: 100%;
    }
    
    .nav-btn {
        text-align: center;
    }
}
